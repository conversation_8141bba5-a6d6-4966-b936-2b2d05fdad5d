import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON>Text, <PERSON>fresh<PERSON><PERSON>, <PERSON><PERSON>hart, ChevronRight, TrendingUp } from 'lucide-react'
import { cn } from '../../../utils/cn'
import type { TaskSetsMetrics } from '../../../services/management/managementService'

interface TaskSetsAnalyticsCardProps {
  data: TaskSetsMetrics | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

// Custom Sunburst Chart Component
const SunburstChart: React.FC<{ data: TaskSetsMetrics }> = ({ data }) => {
  const [selectedGenType, setSelectedGenType] = useState<string | null>(null)

  const colors = [
    'from-blue-500 to-blue-600',
    'from-emerald-500 to-emerald-600',
    'from-purple-500 to-purple-600',
    'from-orange-500 to-orange-600',
    'from-pink-500 to-pink-600',
    'from-indigo-500 to-indigo-600',
    'from-teal-500 to-teal-600',
    'from-red-500 to-red-600'
  ]

  const totalTaskSets = data.hierarchy?.reduce((sum, item) => sum + item.task_sets_count, 0) || 1

  return (
    <div className="relative w-full h-full flex flex-col items-center justify-center">
      {/* Center circle with total */}
      <motion.div
        className="absolute inset-0 flex items-center justify-center"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
      >
        <div className="bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-900 rounded-full w-32 h-32 flex flex-col items-center justify-center border-4 border-white dark:border-slate-700 shadow-lg">
          <motion.div
            className="text-2xl font-bold text-slate-800 dark:text-slate-200"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
          >
            {totalTaskSets}
          </motion.div>
          <motion.div
            className="text-xs text-slate-600 dark:text-slate-400 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
          >
            Total Sets
          </motion.div>
        </div>
      </motion.div>

      {/* Outer ring - Generation Types */}
      <div className="relative w-80 h-80">
        {data.hierarchy?.map((genType, index) => {
          const percentage = (genType.task_sets_count / totalTaskSets) * 100
          const angle = (percentage / 100) * 360
          const rotation = index * (360 / data.hierarchy!.length)

          return (
            <motion.div
              key={genType.generation_type}
              className="absolute inset-0"
              style={{ transform: `rotate(${rotation}deg)` }}
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1, type: "spring", stiffness: 150 }}
            >
              <motion.div
                className={cn(
                  "absolute top-0 left-1/2 w-20 h-20 -ml-10 -mt-10 rounded-full cursor-pointer",
                  `bg-gradient-to-br ${colors[index % colors.length]}`,
                  "shadow-lg hover:shadow-xl transition-all duration-300",
                  selectedGenType === genType.generation_type && "ring-4 ring-white dark:ring-slate-700"
                )}
                style={{
                  transformOrigin: '50% 160px',
                }}
                whileHover={{ scale: 1.1, y: -5 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setSelectedGenType(
                  selectedGenType === genType.generation_type ? null : genType.generation_type
                )}
              >
                <div className="absolute inset-0 flex flex-col items-center justify-center text-white text-xs font-medium p-2 text-center">
                  <div className="font-bold">{genType.task_sets_count}</div>
                  <div className="text-[10px] leading-tight">{genType.generation_type}</div>
                </div>
              </motion.div>
            </motion.div>
          )
        })}
      </div>

      {/* Task Item Types Detail Panel */}
      <AnimatePresence>
        {selectedGenType && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="absolute bottom-0 left-0 right-0 bg-white dark:bg-slate-800 rounded-lg p-4 shadow-lg border border-slate-200 dark:border-slate-700"
          >
            <div className="flex items-center gap-2 mb-3">
              <ChevronRight className="h-4 w-4 text-slate-600 dark:text-slate-400" />
              <span className="font-medium text-slate-800 dark:text-slate-200">{selectedGenType}</span>
            </div>
            <div className="grid grid-cols-2 gap-2">
              {data.hierarchy?.find(g => g.generation_type === selectedGenType)?.task_item_types?.map((taskType, index) => (
                <motion.div
                  key={taskType.task_item_type}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-700 rounded text-sm"
                >
                  <span className="text-slate-700 dark:text-slate-300">{taskType.task_item_type}</span>
                  <span className="font-medium text-slate-800 dark:text-slate-200">{taskType.task_items_count}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

/**
 * Task Sets Analytics Card - Displays task sets creation and completion trends
 * Features animated charts and type distributions
 */
const TaskSetsAnalyticsCard: React.FC<TaskSetsAnalyticsCardProps> = ({
  data,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {

  if (loading) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden"
      >
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-muted/20 to-transparent animate-pulse" />
        <div className="relative space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-5 bg-muted rounded w-32 animate-pulse" />
              <div className="h-4 bg-muted rounded w-48 animate-pulse" />
            </div>
            <div className="h-8 w-8 bg-muted rounded animate-pulse" />
          </div>
          <div className="grid grid-cols-3 gap-4 mb-6">
            <div className="h-20 bg-muted rounded animate-pulse" />
            <div className="h-20 bg-muted rounded animate-pulse" />
            <div className="h-20 bg-muted rounded animate-pulse" />
          </div>
          <div className="h-96 bg-muted rounded animate-pulse" />
        </div>
      </motion.div>
    )
  }

  if (error) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-destructive/20 rounded-xl p-6"
      >
        <div className="text-center space-y-4">
          <div className="text-destructive font-medium">Failed to load task sets analytics</div>
          <p className="text-sm text-muted-foreground">{error}</p>
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            <RefreshCw className="h-4 w-4 inline mr-2" />
            Retry
          </motion.button>
        </div>
      </motion.div>
    )
  }

  if (!data) return null

  return (
    <motion.div
      variants={cardVariants}
      className="bg-card border border-border rounded-xl p-6 relative overflow-hidden"
    >
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 via-transparent to-red-500/5" />
      
      <div className="relative">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
              <FileText className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              Task Sets Analytics
            </h3>
            <p className="text-sm text-muted-foreground">
              {data.date_range.start_date} to {data.date_range.end_date}
            </p>
          </div>
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.1, rotate: 180 }}
            whileTap={{ scale: 0.9 }}
            className="p-2 hover:bg-muted rounded-md transition-colors"
            title="Refresh analytics"
          >
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </motion.button>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-lg p-3"
          >
            <div className="text-xs font-medium text-orange-700 dark:text-orange-300 mb-1">
              All Time
            </div>
            <div className="text-lg font-bold text-orange-800 dark:text-orange-200">
              {data.summary.total_task_sets_all_time.toLocaleString()}
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-3"
          >
            <div className="text-xs font-medium text-blue-700 dark:text-blue-300 mb-1">
              Created
            </div>
            <div className="text-lg font-bold text-blue-800 dark:text-blue-200">
              {data.summary.total_task_sets_in_range.toLocaleString()}
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-3"
          >
            <div className="text-xs font-medium text-green-700 dark:text-green-300 mb-1">
              All Time Sets
            </div>
            <div className="text-lg font-bold text-green-800 dark:text-green-200">
              {data.summary.total_task_sets_all_time.toLocaleString()}
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-3"
          >
            <div className="text-xs font-medium text-purple-700 dark:text-purple-300 mb-1">
              Items
            </div>
            <div className="text-lg font-bold text-purple-800 dark:text-purple-200">
              {data.summary.total_task_items_in_range.toLocaleString()}
            </div>
          </motion.div>
        </div>

        {/* Hierarchical Distribution - Sunburst Chart - Full Width */}
        <div className="w-full">
          <h4 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-3">
            <PieChart className="h-5 w-5 text-emerald-600" />
            Generation Types & Task Items Hierarchy
            <span className="text-sm text-muted-foreground font-normal">
              Interactive drill-down visualization
            </span>
          </h4>
          <div className="h-[600px] bg-gradient-to-br from-emerald-50/30 via-blue-50/30 to-purple-50/30 dark:from-emerald-900/10 dark:via-blue-900/10 dark:to-purple-900/10 rounded-xl p-6 border border-border shadow-inner">
            <div
              ref={sunburstChartRef}
              style={{ height: '100%', width: '100%' }}
            />
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default TaskSetsAnalyticsCard
