import React, { useRef, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { <PERSON>Tex<PERSON>, RefreshCw, PieChart } from 'lucide-react'
import * as echarts from 'echarts'
import { cn } from '../../../utils/cn'
import type { TaskSetsMetrics } from '../../../services/management/managementService'

interface TaskSetsAnalyticsCardProps {
  data: TaskSetsMetrics | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

/**
 * Task Sets Analytics Card - Displays task sets creation and completion trends
 * Features animated charts and type distributions
 */
const TaskSetsAnalyticsCard: React.FC<TaskSetsAnalyticsCardProps> = ({
  data,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {
  // Chart refs
  const sunburstChartRef = useRef<HTMLDivElement>(null)
  const sunburstChartInstance = useRef<echarts.ECharts | null>(null)

  // Cleanup function
  const cleanup = useCallback(() => {
    if (sunburstChartInstance.current) {
      sunburstChartInstance.current.dispose()
      sunburstChartInstance.current = null
    }
  }, [])

  // Initialize charts when DOM is ready
  useEffect(() => {
    if (!sunburstChartRef.current) return

    // Clean up existing instances
    cleanup()

    try {
      // Initialize sunburst chart
      sunburstChartInstance.current = echarts.init(sunburstChartRef.current)

      // Add resize listeners
      const handleResize = () => {
        sunburstChartInstance.current?.resize()
      }

      window.addEventListener('resize', handleResize)

      return () => {
        window.removeEventListener('resize', handleResize)
        cleanup()
      }
    } catch (error) {
      console.error('Error initializing charts:', error)
      cleanup()
    }
  }, [cleanup])

  // Update charts when data changes
  useEffect(() => {
    if (!data || !sunburstChartInstance.current) {
      return
    }

    try {
      // Prepare chart data - handle both new hierarchy and legacy structure
      let creationDates: string[] = []
      let creationCounts: number[] = []

      if (data.task_sets?.daily_creation) {
        // Legacy structure
        creationDates = data.task_sets.daily_creation.map(item =>
          new Date(item._id).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
        )
        creationCounts = data.task_sets.daily_creation.map(item => item.count)
      } else {
        // For now, create dummy data for creation chart since new API doesn't have daily_creation
        // You might want to add this to your API response
        creationDates = ['Today']
        creationCounts = [data.summary.total_task_sets_in_range]
      }

      // Interactive Line Race Chart for Creation
      const creationChartOption = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#e5e7eb',
          borderWidth: 1,
          textStyle: { color: '#374151', fontSize: 12 },
          formatter: (params: any) => {
            const param = params[0]
            return `<div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${param.name}</div>
              <div style="display: flex; align-items: center; gap: 8px;">
                <div style="width: 10px; height: 10px; background: ${param.color}; border-radius: 50%;"></div>
                <span>${param.seriesName}: <strong>${param.value}</strong></span>
              </div>
            </div>`
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '15%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: creationDates,
          axisLine: {
            show: true,
            lineStyle: { color: '#e5e7eb', width: 1 }
          },
          axisTick: { show: false },
          axisLabel: {
            color: '#6b7280',
            fontSize: 11,
            interval: 0,
            rotate: 45,
            margin: 10
          }
        },
        yAxis: {
          type: 'value',
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: { color: '#6b7280', fontSize: 11 },
          splitLine: {
            lineStyle: {
              color: '#f3f4f6',
              type: 'dashed'
            }
          }
        },
        series: [{
          name: 'Task Sets Created',
          data: creationCounts,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          showSymbol: true,
          lineStyle: {
            width: 4,
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 1, y2: 0,
              colorStops: [
                { offset: 0, color: '#3b82f6' },
                { offset: 0.5, color: '#60a5fa' },
                { offset: 1, color: '#93c5fd' }
              ]
            },
            shadowColor: 'rgba(59, 130, 246, 0.3)',
            shadowBlur: 10,
            shadowOffsetY: 3
          },
          itemStyle: {
            color: '#3b82f6',
            borderColor: '#ffffff',
            borderWidth: 3,
            shadowColor: 'rgba(59, 130, 246, 0.5)',
            shadowBlur: 8
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(59, 130, 246, 0.4)' },
                { offset: 0.5, color: 'rgba(59, 130, 246, 0.2)' },
                { offset: 1, color: 'rgba(59, 130, 246, 0.05)' }
              ]
            }
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              shadowBlur: 20,
              shadowColor: 'rgba(59, 130, 246, 0.8)'
            }
          },
          animationDuration: 2000
        }],
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          },
          {
            type: 'slider',
            show: creationDates.length > 7,
            start: 0,
            end: 100,
            height: 20,
            bottom: 5,
            textStyle: {
              color: '#6b7280',
              fontSize: 10
            },
            borderColor: '#e5e7eb',
            fillerColor: 'rgba(59, 130, 246, 0.2)',
            handleStyle: {
              color: '#3b82f6',
              borderColor: '#ffffff'
            }
          }
        ],
        animation: true,
        animationThreshold: 2000,
        animationDuration: 1000,
        animationDelay: (idx: number) => idx * 100,
        animationDurationUpdate: 300,
        animationDelayUpdate: (idx: number) => idx * 50
      }

      // Simple sunburst data
      const sunburstData = data.hierarchy?.map((genType) => ({
        name: genType.generation_type,
        value: genType.task_sets_count,
        children: genType.task_item_types?.map((taskType) => ({
          name: taskType.task_item_type,
          value: taskType.task_items_count
        })) || []
      })) || []


      // Simple default sunburst chart
      const sunburstChartOption = {
        series: [{
          type: 'sunburst',
          data: sunburstData,
          radius: [0, '90%'],
          center: ['50%', '50%']
        }]
      }

      // Update charts
      creationChartInstance.current.setOption(creationChartOption, true)
      sunburstChartInstance.current.setOption(sunburstChartOption, true)
    } catch (error) {
      console.error('Error updating charts:', error)
    }
  }, [data])

  if (loading) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden"
      >
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-muted/20 to-transparent animate-pulse" />
        <div className="relative space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-5 bg-muted rounded w-32 animate-pulse" />
              <div className="h-4 bg-muted rounded w-48 animate-pulse" />
            </div>
            <div className="h-8 w-8 bg-muted rounded animate-pulse" />
          </div>
          <div className="grid grid-cols-3 gap-4 mb-6">
            <div className="h-20 bg-muted rounded animate-pulse" />
            <div className="h-20 bg-muted rounded animate-pulse" />
            <div className="h-20 bg-muted rounded animate-pulse" />
          </div>
          <div className="h-96 bg-muted rounded animate-pulse" />
        </div>
      </motion.div>
    )
  }

  if (error) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-destructive/20 rounded-xl p-6"
      >
        <div className="text-center space-y-4">
          <div className="text-destructive font-medium">Failed to load task sets analytics</div>
          <p className="text-sm text-muted-foreground">{error}</p>
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            <RefreshCw className="h-4 w-4 inline mr-2" />
            Retry
          </motion.button>
        </div>
      </motion.div>
    )
  }

  if (!data) return null

  return (
    <motion.div
      variants={cardVariants}
      className="bg-card border border-border rounded-xl p-6 relative overflow-hidden"
    >
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 via-transparent to-red-500/5" />
      
      <div className="relative">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
              <FileText className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              Task Sets Analytics
            </h3>
            <p className="text-sm text-muted-foreground">
              {data.date_range.start_date} to {data.date_range.end_date}
            </p>
          </div>
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.1, rotate: 180 }}
            whileTap={{ scale: 0.9 }}
            className="p-2 hover:bg-muted rounded-md transition-colors"
            title="Refresh analytics"
          >
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </motion.button>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-lg p-3"
          >
            <div className="text-xs font-medium text-orange-700 dark:text-orange-300 mb-1">
              All Time
            </div>
            <div className="text-lg font-bold text-orange-800 dark:text-orange-200">
              {data.summary.total_task_sets_all_time.toLocaleString()}
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-3"
          >
            <div className="text-xs font-medium text-blue-700 dark:text-blue-300 mb-1">
              Created
            </div>
            <div className="text-lg font-bold text-blue-800 dark:text-blue-200">
              {data.summary.total_task_sets_in_range.toLocaleString()}
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-3"
          >
            <div className="text-xs font-medium text-green-700 dark:text-green-300 mb-1">
              All Time Sets
            </div>
            <div className="text-lg font-bold text-green-800 dark:text-green-200">
              {data.summary.total_task_sets_all_time.toLocaleString()}
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-3"
          >
            <div className="text-xs font-medium text-purple-700 dark:text-purple-300 mb-1">
              Items
            </div>
            <div className="text-lg font-bold text-purple-800 dark:text-purple-200">
              {data.summary.total_task_items_in_range.toLocaleString()}
            </div>
          </motion.div>
        </div>

        {/* Hierarchical Distribution - Sunburst Chart - Full Width */}
        <div className="w-full">
          <h4 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-3">
            <PieChart className="h-5 w-5 text-emerald-600" />
            Generation Types & Task Items Hierarchy
            <span className="text-sm text-muted-foreground font-normal">
              Interactive drill-down visualization
            </span>
          </h4>
          <div className="h-[600px] bg-gradient-to-br from-emerald-50/30 via-blue-50/30 to-purple-50/30 dark:from-emerald-900/10 dark:via-blue-900/10 dark:to-purple-900/10 rounded-xl p-6 border border-border shadow-inner">
            <div
              ref={sunburstChartRef}
              style={{ height: '100%', width: '100%' }}
            />
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default TaskSetsAnalyticsCard
