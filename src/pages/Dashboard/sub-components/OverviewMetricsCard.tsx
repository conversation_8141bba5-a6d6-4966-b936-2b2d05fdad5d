import React from 'react'
import { motion } from 'framer-motion'
import { Users, UserPlus, Activity, FileText, Plus, CheckCircle, TrendingUp, RefreshCw } from 'lucide-react'
import { cn } from '../../../utils/cn'
import type { DashboardOverview } from '../../../services/management/managementService'

interface OverviewMetricsCardProps {
  data: DashboardOverview | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

/**
 * Overview Metrics Card - Displays key dashboard metrics
 * Sleek design with animated counters and hover effects
 */
const OverviewMetricsCard: React.FC<OverviewMetricsCardProps> = ({
  data,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {
  if (loading) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden"
      >
        {/* Animated loading background */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-muted/20 to-transparent animate-pulse" />
        <div className="relative space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-5 bg-muted rounded w-32 animate-pulse" />
              <div className="h-4 bg-muted rounded w-48 animate-pulse" />
            </div>
            <div className="h-8 w-8 bg-muted rounded animate-pulse" />
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: i * 0.1 }}
                className="bg-background border border-border rounded-lg p-4 space-y-3"
              >
                <div className="flex items-center gap-3">
                  <div className="h-8 w-8 bg-muted rounded-lg animate-pulse" />
                </div>
                <div className="space-y-2">
                  <div className="h-6 bg-muted rounded w-12 animate-pulse" />
                  <div className="h-3 bg-muted rounded w-20 animate-pulse" />
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>
    )
  }

  if (error) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-destructive/20 rounded-xl p-6"
      >
        <div className="text-center space-y-4">
          <div className="text-destructive font-medium">Failed to load metrics</div>
          <p className="text-sm text-muted-foreground">{error}</p>
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            <RefreshCw className="h-4 w-4 inline mr-2" />
            Retry
          </motion.button>
        </div>
      </motion.div>
    )
  }

  if (!data) return null

  const metrics = [
    {
      label: 'Total Users',
      value: data.overview.total_users,
      icon: Users,
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-100 dark:bg-blue-900/20',
      gradient: 'from-blue-500/10 to-blue-600/10'
    },
    {
      label: 'Joined Today',
      value: data.overview.users_joined_today,
      icon: UserPlus,
      color: 'text-emerald-600 dark:text-emerald-400',
      bgColor: 'bg-emerald-100 dark:bg-emerald-900/20',
      gradient: 'from-emerald-500/10 to-emerald-600/10'
    },
    {
      label: 'Active Today',
      value: data.overview.users_active_today,
      icon: Activity,
      color: 'text-purple-600 dark:text-purple-400',
      bgColor: 'bg-purple-100 dark:bg-purple-900/20',
      gradient: 'from-purple-500/10 to-purple-600/10'
    },
    {
      label: 'Total Task Sets',
      value: data.overview.total_task_sets,
      icon: FileText,
      color: 'text-orange-600 dark:text-orange-400',
      bgColor: 'bg-orange-100 dark:bg-orange-900/20',
      gradient: 'from-orange-500/10 to-orange-600/10'
    },
    {
      label: 'Created Today',
      value: data.overview.task_sets_created_today,
      icon: Plus,
      color: 'text-teal-600 dark:text-teal-400',
      bgColor: 'bg-teal-100 dark:bg-teal-900/20',
      gradient: 'from-teal-500/10 to-teal-600/10'
    },
    {
      label: 'Completed Today',
      value: data.overview.task_sets_completed_today,
      icon: CheckCircle,
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-100 dark:bg-green-900/20',
      gradient: 'from-green-500/10 to-green-600/10'
    }
  ]

  return (
    <motion.div
      variants={cardVariants}
      className="bg-card border border-border rounded-xl p-6 relative overflow-hidden"
    >
      {/* Subtle background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5" />

      <div className="relative">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              Overview Metrics
            </h3>
            <p className="text-sm text-muted-foreground">
              Real-time dashboard statistics
            </p>
          </div>
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.1, rotate: 180 }}
            whileTap={{ scale: 0.9 }}
            className="p-2 hover:bg-muted rounded-md transition-colors"
            title="Refresh metrics"
          >
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </motion.button>
        </div>

        {/* Metrics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {metrics.map((metric, index) => (
            <motion.div
              key={metric.label}
              initial={{ opacity: 0, y: 20, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{
                delay: index * 0.1,
                type: "spring",
                stiffness: 100,
                damping: 15
              }}
              whileHover={{
                scale: 1.02,
                y: -2,
                transition: { duration: 0.2 }
              }}
              className={cn(
                "bg-gradient-to-br border border-border rounded-lg p-4 cursor-pointer relative overflow-hidden group",
                metric.gradient
              )}
            >
              {/* Hover effect overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

              <div className="relative">
                <div className="flex items-center gap-3 mb-3">
                  <motion.div
                    className={cn("p-2 rounded-lg", metric.bgColor)}
                    whileHover={{ scale: 1.1 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    <metric.icon className={cn("h-4 w-4", metric.color)} />
                  </motion.div>
                </div>
                <div className="space-y-1">
                  <motion.div
                    className="text-2xl font-bold text-foreground"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: index * 0.1 + 0.2 }}
                  >
                    {metric.value.toLocaleString()}
                  </motion.div>
                  <div className="text-xs text-muted-foreground font-medium">
                    {metric.label}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Timestamp */}
        <motion.div
          className="mt-6 pt-4 border-t border-border"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
        >
          <div className="text-xs text-muted-foreground flex items-center gap-2">
            <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
            Last updated: {new Date(data.timestamp).toLocaleString()}
          </div>
        </motion.div>
      </div>
    </motion.div>
  )
}

export default OverviewMetricsCard
