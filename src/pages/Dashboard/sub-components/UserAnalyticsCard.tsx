import React, { useRef, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { Users, TrendingUp, <PERSON><PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON><PERSON>, Bar<PERSON><PERSON>3 } from 'lucide-react'
import * as echarts from 'echarts'
import { cn } from '../../../utils/cn'
import type { UsersMetrics } from '../../../services/management/managementService'

interface UserAnalyticsCardProps {
  data: UsersMetrics | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

/**
 * User Analytics Card - Displays user registration and activity trends
 * Features animated charts and role distribution
 */
const UserAnalyticsCard: React.FC<UserAnalyticsCardProps> = ({
  data,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {
  // Chart refs
  const registrationChartRef = useRef<HTMLDivElement>(null)
  const activeUsersChartRef = useRef<HTMLDivElement>(null)
  const registrationChartInstance = useRef<echarts.ECharts | null>(null)
  const activeUsersChartInstance = useRef<echarts.ECharts | null>(null)

  // Cleanup function
  const cleanup = useCallback(() => {
    if (registrationChartInstance.current) {
      registrationChartInstance.current.dispose()
      registrationChartInstance.current = null
    }
    if (activeUsersChartInstance.current) {
      activeUsersChartInstance.current.dispose()
      activeUsersChartInstance.current = null
    }
  }, [])

  // Initialize charts when DOM is ready
  useEffect(() => {
    if (!registrationChartRef.current || !activeUsersChartRef.current) return

    // Clean up existing instances
    cleanup()

    try {
      // Initialize registration chart
      registrationChartInstance.current = echarts.init(registrationChartRef.current)

      // Initialize active users chart
      activeUsersChartInstance.current = echarts.init(activeUsersChartRef.current)



      // Add resize listeners
      const handleResize = () => {
        registrationChartInstance.current?.resize()
        activeUsersChartInstance.current?.resize()
      }

      window.addEventListener('resize', handleResize)

      return () => {
        window.removeEventListener('resize', handleResize)
        cleanup()
      }
    } catch (error) {
      console.error('Error initializing user charts:', error)
      cleanup()
    }
  }, [cleanup])

  // Update charts when data changes
  useEffect(() => {
    if (!data || !registrationChartInstance.current || !activeUsersChartInstance.current) {
      return
    }

    try {
      // Prepare chart data
      const registrationDates = data.daily_data.registrations.map(item =>
        new Date(item._id).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
      )
      const registrationCounts = data.daily_data.registrations.map(item => item.count)
      const activeUserDates = data.daily_data.active_users.map(item =>
        new Date(item._id).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
      )
      const activeUserCounts = data.daily_data.active_users.map(item => item.count)

      // Interactive Registration chart options
      const registrationChartOption = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#e5e7eb',
          borderWidth: 1,
          textStyle: { color: '#374151', fontSize: 12 },
          formatter: (params: any) => {
            const param = params[0]
            return `<div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${param.name}</div>
              <div style="display: flex; align-items: center; gap: 8px;">
                <div style="width: 10px; height: 10px; background: ${param.color}; border-radius: 50%;"></div>
                <span>${param.seriesName}: <strong>${param.value}</strong></span>
              </div>
            </div>`
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '15%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: registrationDates,
          axisLine: {
            show: true,
            lineStyle: { color: '#e5e7eb', width: 1 }
          },
          axisTick: { show: false },
          axisLabel: {
            color: '#6b7280',
            fontSize: 11,
            interval: 0,
            rotate: 45,
            margin: 10
          }
        },
        yAxis: {
          type: 'value',
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: { color: '#6b7280', fontSize: 11 },
          splitLine: {
            lineStyle: {
              color: '#f3f4f6',
              type: 'dashed'
            }
          }
        },
        series: [{
          name: 'New Registrations',
          data: registrationCounts,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          showSymbol: true,
          lineStyle: {
            width: 4,
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 1, y2: 0,
              colorStops: [
                { offset: 0, color: '#10b981' },
                { offset: 0.5, color: '#34d399' },
                { offset: 1, color: '#6ee7b7' }
              ]
            },
            shadowColor: 'rgba(16, 185, 129, 0.3)',
            shadowBlur: 10,
            shadowOffsetY: 3
          },
          itemStyle: {
            color: '#10b981',
            borderColor: '#ffffff',
            borderWidth: 3,
            shadowColor: 'rgba(16, 185, 129, 0.5)',
            shadowBlur: 8
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(16, 185, 129, 0.4)' },
                { offset: 0.5, color: 'rgba(16, 185, 129, 0.2)' },
                { offset: 1, color: 'rgba(16, 185, 129, 0.05)' }
              ]
            }
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              shadowBlur: 20,
              shadowColor: 'rgba(16, 185, 129, 0.8)'
            }
          },
          animationDuration: 2000
        }],
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          },
          {
            type: 'slider',
            show: registrationDates.length > 7,
            start: 0,
            end: 100,
            height: 20,
            bottom: 5,
            textStyle: {
              color: '#6b7280',
              fontSize: 10
            },
            borderColor: '#e5e7eb',
            fillerColor: 'rgba(16, 185, 129, 0.2)',
            handleStyle: {
              color: '#10b981',
              borderColor: '#ffffff'
            }
          }
        ],
        animation: true,
        animationThreshold: 2000,
        animationDuration: 1000,
        animationDelay: (idx: number) => idx * 100,
        animationDurationUpdate: 300,
        animationDelayUpdate: (idx: number) => idx * 50
      }

      // Interactive Active Users chart options
      const activeUsersChartOption = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#e5e7eb',
          borderWidth: 1,
          textStyle: { color: '#374151', fontSize: 12 },
          formatter: (params: any) => {
            const param = params[0]
            return `<div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${param.name}</div>
              <div style="display: flex; align-items: center; gap: 8px;">
                <div style="width: 10px; height: 10px; background: ${param.color}; border-radius: 50%;"></div>
                <span>${param.seriesName}: <strong>${param.value}</strong></span>
              </div>
            </div>`
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '15%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: activeUserDates,
          axisLine: {
            show: true,
            lineStyle: { color: '#e5e7eb', width: 1 }
          },
          axisTick: { show: false },
          axisLabel: {
            color: '#6b7280',
            fontSize: 11,
            interval: 0,
            rotate: 45,
            margin: 10
          }
        },
        yAxis: {
          type: 'value',
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: { color: '#6b7280', fontSize: 11 },
          splitLine: {
            lineStyle: {
              color: '#f3f4f6',
              type: 'dashed'
            }
          }
        },
        series: [{
          name: 'Active Users',
          data: activeUserCounts,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          showSymbol: true,
          lineStyle: {
            width: 4,
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 1, y2: 0,
              colorStops: [
                { offset: 0, color: '#8b5cf6' },
                { offset: 0.5, color: '#a78bfa' },
                { offset: 1, color: '#c4b5fd' }
              ]
            },
            shadowColor: 'rgba(139, 92, 246, 0.3)',
            shadowBlur: 10,
            shadowOffsetY: 3
          },
          itemStyle: {
            color: '#8b5cf6',
            borderColor: '#ffffff',
            borderWidth: 3,
            shadowColor: 'rgba(139, 92, 246, 0.5)',
            shadowBlur: 8
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(139, 92, 246, 0.4)' },
                { offset: 0.5, color: 'rgba(139, 92, 246, 0.2)' },
                { offset: 1, color: 'rgba(139, 92, 246, 0.05)' }
              ]
            }
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              shadowBlur: 20,
              shadowColor: 'rgba(139, 92, 246, 0.8)'
            }
          },
          animationDuration: 2000
        }],
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          },
          {
            type: 'slider',
            show: activeUserDates.length > 7,
            start: 0,
            end: 100,
            height: 20,
            bottom: 5,
            textStyle: {
              color: '#6b7280',
              fontSize: 10
            },
            borderColor: '#e5e7eb',
            fillerColor: 'rgba(139, 92, 246, 0.2)',
            handleStyle: {
              color: '#8b5cf6',
              borderColor: '#ffffff'
            }
          }
        ],
        animation: true,
        animationThreshold: 2000,
        animationDuration: 1000,
        animationDelay: (idx: number) => idx * 100,
        animationDurationUpdate: 300,
        animationDelayUpdate: (idx: number) => idx * 50
      }

      // Update charts
      registrationChartInstance.current.setOption(registrationChartOption, true)
      activeUsersChartInstance.current.setOption(activeUsersChartOption, true)
    } catch (error) {
      console.error('Error updating user charts:', error)
    }
  }, [data])

  if (loading) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden"
      >
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-muted/20 to-transparent animate-pulse" />
        <div className="relative space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-5 bg-muted rounded w-32 animate-pulse" />
              <div className="h-4 bg-muted rounded w-48 animate-pulse" />
            </div>
            <div className="h-8 w-8 bg-muted rounded animate-pulse" />
          </div>
          <div className="space-y-4">
            <div className="h-32 bg-muted rounded animate-pulse" />
            <div className="grid grid-cols-2 gap-4">
              <div className="h-20 bg-muted rounded animate-pulse" />
              <div className="h-20 bg-muted rounded animate-pulse" />
            </div>
          </div>
        </div>
      </motion.div>
    )
  }

  if (error) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-destructive/20 rounded-xl p-6"
      >
        <div className="text-center space-y-4">
          <div className="text-destructive font-medium">Failed to load user analytics</div>
          <p className="text-sm text-muted-foreground">{error}</p>
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            <RefreshCw className="h-4 w-4 inline mr-2" />
            Retry
          </motion.button>
        </div>
      </motion.div>
    )
  }

  if (!data) return null

  return (
    <motion.div
      variants={cardVariants}
      className="bg-card border border-border rounded-xl p-6 relative overflow-hidden"
    >
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5" />
      
      <div className="relative">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              User Analytics
            </h3>
            <p className="text-sm text-muted-foreground">
              {data.date_range.start_date} to {data.date_range.end_date}
            </p>
          </div>
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.1, rotate: 180 }}
            whileTap={{ scale: 0.9 }}
            className="p-2 hover:bg-muted rounded-md transition-colors"
            title="Refresh analytics"
          >
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </motion.button>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4"
          >
            <div className="flex items-center gap-2 mb-2">
              <Users className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              <span className="text-sm font-medium text-blue-700 dark:text-blue-300">Total Users</span>
            </div>
            <div className="text-2xl font-bold text-blue-800 dark:text-blue-200">
              {data.summary.total_users.toLocaleString()}
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-4"
          >
            <div className="flex items-center gap-2 mb-2">
              <UserCheck className="h-4 w-4 text-green-600 dark:text-green-400" />
              <span className="text-sm font-medium text-green-700 dark:text-green-300">In Period</span>
            </div>
            <div className="text-2xl font-bold text-green-800 dark:text-green-200">
              {data.summary.users_in_period.toLocaleString()}
            </div>
          </motion.div>
        </div>

        {/* Registration Chart */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-foreground mb-3 flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-emerald-600" />
            Daily Registrations
          </h4>
          <div className="h-48 bg-gradient-to-br from-emerald-50/50 to-emerald-100/50 dark:from-emerald-900/10 dark:to-emerald-800/10 rounded-lg p-2">
            <div
              ref={registrationChartRef}
              style={{ height: '100%', width: '100%' }}
            />
          </div>
        </div>

        {/* Active Users Chart */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-foreground mb-3 flex items-center gap-2">
            <BarChart3 className="h-4 w-4 text-purple-600" />
            Daily Active Users
          </h4>
          <div className="h-48 bg-gradient-to-br from-purple-50/50 to-purple-100/50 dark:from-purple-900/10 dark:to-purple-800/10 rounded-lg p-2">
            <div
              ref={activeUsersChartRef}
              style={{ height: '100%', width: '100%' }}
            />
          </div>
        </div>

        {/* Role Distribution */}
        <div>
          <h4 className="text-sm font-semibold text-foreground mb-3">Role Distribution</h4>
          <div className="space-y-2">
            {data.distribution.by_role.map((role, index) => (
              <motion.div
                key={role._id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between p-2 bg-muted/50 rounded-lg"
              >
                <span className="text-sm font-medium text-foreground capitalize">
                  {role._id}
                </span>
                <span className="text-sm text-muted-foreground">
                  {role.count} users
                </span>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default UserAnalyticsCard
